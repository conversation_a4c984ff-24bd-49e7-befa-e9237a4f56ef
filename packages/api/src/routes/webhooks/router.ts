import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { reflowpay, retryWebhookDelivery, WebhookEventType } from "@repo/payments";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { ensureUserIsPartOfOrganization } from "../../utils/organization";
import { webhooks } from "@repo/payments";
import { pixWebhooksRouter } from "./pix/router";
import { svixRouter } from "./svix/router";

export const webhooksRouter = new Hono()
  // Não definimos basePath aqui, pois ele é definido no app.ts como "/webhooks"
  .basePath("");

// Mount PIX webhooks router
webhooksRouter.route("/pix", pixWebhooksRouter);

// Mount SVIX webhooks router
webhooksRouter.route("/svix", svixRouter);

// ReflowPay webhook handler
webhooksRouter.post(
  "/reflowpay",
  describeRoute({
    tags: ["Webhooks"],
    summary: "ReflowPay webhook handler",
    description: "Handles webhooks from ReflowPay",
    responses: {
      200: {
        description: "Webhook processed successfully",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      // Pass the request to the ReflowPay webhook handler
      return await reflowpay.webhookHandler(c.req);
    } catch (error) {
      logger.error("Error processing ReflowPay webhook", { error });
      return c.json({ error: "Failed to process webhook" }, 500);
    }
  }
);

// List webhooks for an organization
webhooksRouter.get(
  "/",
  validator("query", z.object({
    organizationId: z.string(),
  })),
  authMiddleware,
  describeRoute({
    tags: ["Webhooks"],
    summary: "List webhooks",
    description: "Lists all webhooks for an organization",
    responses: {
      200: {
        description: "Webhooks retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const { organizationId } = c.req.valid("query");

      // Get webhooks
      const webhooks = await db.webhook.findMany({
        where: {
          organizationId,
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return c.json({
        data: webhooks.map(webhook => ({
          id: webhook.id,
          url: webhook.url,
          events: webhook.events,
          isActive: webhook.isActive,
          createdAt: webhook.createdAt,
          updatedAt: webhook.updatedAt,
        })),
      });
    } catch (error) {
      logger.error("Error listing webhooks", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to list webhooks" });
    }
  })
);

// Create a new webhook
webhooksRouter.post(
  "/",
  validator("json", z.object({
    url: z.string().url(),
    events: z.array(z.string()),
    organizationId: z.string(),
    isActive: z.boolean().default(true),
  })),
  authMiddleware,
  describeRoute({
    tags: ["Webhooks"],
    summary: "Create a new webhook",
    description: "Creates a new webhook for an organization",
    responses: {
      201: {
        description: "Webhook created successfully",
      },
      400: {
        description: "Invalid request data",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const {
        url,
        events,
        organizationId,
        isActive
      } = c.req.valid("json");

      // Check if the URL already exists for this organization
      const existingWebhook = await db.webhook.findFirst({
        where: {
          url,
          organizationId,
        },
      });

      if (existingWebhook) {
        throw new HTTPException(400, { message: "A webhook with this URL already exists for this organization" });
      }

      // Create the webhook
      const webhook = await db.webhook.create({
        data: {
          url,
          events,
          organizationId,
          isActive,
          secret: `whsec_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`,
        },
      });

      return c.json({
        id: webhook.id,
        url: webhook.url,
        events: webhook.events,
        isActive: webhook.isActive,
        secret: webhook.secret,
        createdAt: webhook.createdAt,
        updatedAt: webhook.updatedAt,
      }, 201);
    } catch (error) {
      logger.error("Error creating webhook", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to create webhook" });
    }
  })
);

// Update a webhook
webhooksRouter.patch(
  "/:id",
  validator("param", z.object({
    id: z.string(),
  })),
  validator("json", z.object({
    url: z.string().url().optional(),
    events: z.array(z.string()).optional(),
    isActive: z.boolean().optional(),
    regenerateSecret: z.boolean().optional(),
  })),
  authMiddleware,
  describeRoute({
    tags: ["Webhooks"],
    summary: "Update a webhook",
    description: "Updates a webhook",
    responses: {
      200: {
        description: "Webhook updated successfully",
      },
      400: {
        description: "Invalid request data",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Webhook not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const { id } = c.req.valid("param");
      const {
        url,
        events,
        isActive,
        regenerateSecret
      } = c.req.valid("json");

      // Get the webhook
      const webhook = await db.webhook.findUnique({
        where: { id },
      });

      if (!webhook) {
        throw new HTTPException(404, { message: "Webhook not found" });
      }

      // If URL is being changed, check if it already exists
      if (url && url !== webhook.url) {
        const existingWebhook = await db.webhook.findFirst({
          where: {
            url,
            organizationId: webhook.organizationId,
            id: { not: id },
          },
        });

        if (existingWebhook) {
          throw new HTTPException(400, { message: "A webhook with this URL already exists for this organization" });
        }
      }

      // Prepare update data
      const updateData: any = {
        url,
        events,
        isActive,
      };

      // Regenerate secret if requested
      if (regenerateSecret) {
        updateData.secret = `whsec_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
      }

      // Update the webhook
      const updatedWebhook = await db.webhook.update({
        where: { id },
        data: updateData,
      });

      return c.json({
        id: updatedWebhook.id,
        url: updatedWebhook.url,
        events: updatedWebhook.events,
        isActive: updatedWebhook.isActive,
        secret: updatedWebhook.secret,
        createdAt: updatedWebhook.createdAt,
        updatedAt: updatedWebhook.updatedAt,
      });
    } catch (error) {
      logger.error("Error updating webhook", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to update webhook" });
    }
  })
);

// Delete a webhook
webhooksRouter.delete(
  "/:id",
  validator("param", z.object({
    id: z.string(),
  })),
  authMiddleware,
  describeRoute({
    tags: ["Webhooks"],
    summary: "Delete a webhook",
    description: "Deletes a webhook",
    responses: {
      200: {
        description: "Webhook deleted successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Webhook not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const { id } = c.req.valid("param");

      // Get the webhook
      const webhook = await db.webhook.findUnique({
        where: { id },
      });

      if (!webhook) {
        throw new HTTPException(404, { message: "Webhook not found" });
      }

      // Delete the webhook
      await db.webhook.delete({
        where: { id },
      });

      return c.json({
        success: true,
      });
    } catch (error) {
      logger.error("Error deleting webhook", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to delete webhook" });
    }
  })
);

// Get webhook events for an organization
webhooksRouter.get(
  "/events",
  validator("query", z.object({
    organizationId: z.string(),
    limit: z.string().optional().default("10").transform(Number),
    offset: z.string().optional().default("0").transform(Number),
    type: z.string().optional(),
  })),
  authMiddleware,
  describeRoute({
    tags: ["Webhooks"],
    summary: "List webhook events",
    description: "Lists webhook events for an organization",
    responses: {
      200: {
        description: "Webhook events retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const { organizationId, limit, offset, type } = c.req.valid("query");

      // Build the where clause
      const where: any = { organizationId };
      if (type) {
        where.type = type;
      }

      // Get the total count
      const total = await db.webhookEvent.count({ where });

      // Get the events
      const events = await db.webhookEvent.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        take: limit,
        skip: offset,
        include: {
          transaction: {
            select: {
              id: true,
              externalId: true,
              referenceCode: true,
              status: true,
              type: true,
              amount: true,
              createdAt: true,
              paymentAt: true,
            },
          },
        },
      });

      return c.json({
        data: events,
        meta: {
          total,
          limit,
          offset,
        },
      });
    } catch (error) {
      logger.error("Error listing webhook events", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to list webhook events" });
    }
  })
);

// Get webhook deliveries for an event
webhooksRouter.get(
  "/events/:eventId/deliveries",
  validator("param", z.object({
    eventId: z.string(),
  })),
  authMiddleware,
  describeRoute({
    tags: ["Webhooks"],
    summary: "List webhook deliveries for an event",
    description: "Lists webhook deliveries for a specific event",
    responses: {
      200: {
        description: "Webhook deliveries retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Event not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const { eventId } = c.req.valid("param");

      // Check if the event exists
      const event = await db.webhookEvent.findUnique({
        where: { id: eventId },
      });

      if (!event) {
        throw new HTTPException(404, { message: "Webhook event not found" });
      }

      // Get the deliveries
      const deliveries = await db.webhookDelivery.findMany({
        where: {
          eventId,
        },
        include: {
          webhook: {
            select: {
              id: true,
              url: true,
              events: true,
              isActive: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return c.json({
        data: deliveries,
      });
    } catch (error) {
      logger.error("Error listing webhook deliveries", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to list webhook deliveries" });
    }
  })
);

// Retry a webhook delivery
webhooksRouter.post(
  "/deliveries/:id/retry",
  validator("param", z.object({
    id: z.string(),
  })),
  authMiddleware,
  describeRoute({
    tags: ["Webhooks"],
    summary: "Retry a webhook delivery",
    description: "Retries a failed webhook delivery",
    responses: {
      200: {
        description: "Webhook delivery retry scheduled",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Delivery not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const { id } = c.req.valid("param");

      // Get the delivery
      const delivery = await db.webhookDelivery.findUnique({
        where: { id },
        include: {
          webhook: {
            select: {
              organizationId: true,
            },
          },
        },
      });

      if (!delivery) {
        throw new HTTPException(404, { message: "Webhook delivery not found" });
      }

      // Check permissions (user must be part of the organization)
      const user = c.get("user");
      await ensureUserIsPartOfOrganization(user.id, delivery.webhook.organizationId);

      // Retry the delivery
      const result = await webhooks.retryWebhookDelivery(id);

      return c.json(result);
    } catch (error) {
      logger.error("Error retrying webhook delivery", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to retry webhook delivery" });
    }
  })
);

// Get available webhook event types
webhooksRouter.get(
  "/event-types",
  authMiddleware,
  describeRoute({
    tags: ["Webhooks"],
    summary: "Get available webhook event types",
    description: "Returns a list of all available webhook event types",
    responses: {
      200: {
        description: "Event types retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      // Get all event types from the enum
      const eventTypes = Object.values(WebhookEventType);

      // Create a comprehensive response with detailed descriptions and payload examples
      const eventTypeDescriptions = [
        // PIX Incoming Events
        {
          type: WebhookEventType.PIX_IN_PROCESSING,
          title: "PIX Recebido - Processando",
          description: "Acionado quando um PIX de entrada está sendo processado pelo sistema",
          detailedDescription: "Este evento é disparado imediatamente quando um PIX é recebido e está sendo validado e processado. Útil para atualizar o status da transação em tempo real.",
          category: "PIX Recebidos",
          payloadExample: {
            id: "cmb2rephm0001yo103xz9i0gm",
            type: "CHARGE",
            status: "PROCESSING",
            amount: 100.00,
            pixKey: "<EMAIL>",
            pixKeyType: "EMAIL",
            description: "Pagamento PIX",
            customerName: "João Silva",
            customerEmail: "<EMAIL>",
            customerDocument: "12345678901",
            createdAt: "2024-01-15T10:30:00Z",
            organizationId: "org_123456789",
            endToEndId: null,
            externalId: "ext_abc123",
            referenceCode: "REF001",
            percentFee: 0,
            fixedFee: 0.50,
            totalFee: 0.50,
            netAmount: 99.50
          }
        },
        {
          type: WebhookEventType.PIX_IN_CONFIRMATION,
          title: "PIX Recebido - Confirmado",
          description: "Acionado quando um PIX de entrada é confirmado com sucesso",
          detailedDescription: "Este evento confirma que o PIX foi processado e o valor foi creditado na conta. Momento ideal para liberar produtos ou serviços.",
          category: "PIX Recebidos",
          payloadExample: {
            id: "cmb2rephm0001yo103xz9i0gm",
            type: "CHARGE",
            status: "APPROVED",
            amount: 100.00,
            pixKey: "<EMAIL>",
            pixKeyType: "EMAIL",
            description: "Pagamento PIX",
            customerName: "João Silva",
            customerEmail: "<EMAIL>",
            customerDocument: "12345678901",
            createdAt: "2024-01-15T10:30:00Z",
            paymentAt: "2024-01-15T10:30:45Z",
            organizationId: "org_123456789",
            endToEndId: "E12345678202401151030ABCDEF123456",
            externalId: "ext_abc123",
            referenceCode: "REF001",
            percentFee: 0,
            fixedFee: 0.50,
            totalFee: 0.50,
            netAmount: 99.50,
            previousStatus: "PROCESSING"
          }
        },

        // PIX Outgoing Events
        {
          type: WebhookEventType.PIX_OUT_PROCESSING,
          title: "PIX Enviado - Processando",
          description: "Acionado quando um PIX de saída está sendo processado",
          detailedDescription: "Este evento é disparado quando uma transferência PIX é iniciada e está sendo processada pelo sistema bancário.",
          category: "PIX Enviados",
          payloadExample: {
            id: "cmb2req920005yo10eq45ot1q",
            type: "SEND",
            status: "PROCESSING",
            amount: 50.00,
            pixKey: "11999887766",
            pixKeyType: "PHONE",
            description: "Transferência PIX",
            customerName: "Maria Santos",
            customerEmail: "<EMAIL>",
            customerDocument: "98765432100",
            createdAt: "2024-01-15T14:20:00Z",
            organizationId: "org_123456789",
            endToEndId: null,
            externalId: "ext_def456",
            referenceCode: "TRF002",
            percentFee: 0,
            fixedFee: 1.00,
            totalFee: 1.00,
            netAmount: 50.00
          }
        },
        {
          type: WebhookEventType.PIX_OUT_CONFIRMATION,
          title: "PIX Enviado - Confirmado",
          description: "Acionado quando um PIX de saída é confirmado com sucesso",
          detailedDescription: "Este evento confirma que a transferência PIX foi processada e concluída com sucesso pelo sistema bancário.",
          category: "PIX Enviados",
          payloadExample: {
            id: "cmb2req920005yo10eq45ot1q",
            type: "SEND",
            status: "APPROVED",
            amount: 50.00,
            pixKey: "11999887766",
            pixKeyType: "PHONE",
            description: "Transferência PIX",
            customerName: "Maria Santos",
            customerEmail: "<EMAIL>",
            customerDocument: "98765432100",
            createdAt: "2024-01-15T14:20:00Z",
            paymentAt: "2024-01-15T14:20:30Z",
            organizationId: "org_123456789",
            endToEndId: "E12345678202401151420GHIJKL789012",
            externalId: "ext_def456",
            referenceCode: "TRF002",
            percentFee: 0,
            fixedFee: 1.00,
            totalFee: 1.00,
            netAmount: 50.00,
            previousStatus: "PROCESSING"
          }
        },
        {
          type: WebhookEventType.PIX_OUT_FAILURE,
          title: "PIX Enviado - Falha",
          description: "Acionado quando um PIX de saída falha",
          detailedDescription: "Este evento é disparado quando uma transferência PIX não pode ser processada devido a erro, rejeição ou cancelamento.",
          category: "PIX Enviados",
          payloadExample: {
            id: "cmb2req920005yo10eq45ot1q",
            type: "SEND",
            status: "REJECTED",
            amount: 50.00,
            pixKey: "11999887766",
            pixKeyType: "PHONE",
            description: "Transferência PIX",
            customerName: "Maria Santos",
            customerEmail: "<EMAIL>",
            customerDocument: "98765432100",
            createdAt: "2024-01-15T14:20:00Z",
            organizationId: "org_123456789",
            endToEndId: null,
            externalId: "ext_def456",
            referenceCode: "TRF002",
            percentFee: 0,
            fixedFee: 1.00,
            totalFee: 1.00,
            netAmount: 50.00,
            previousStatus: "PROCESSING"
          }
        },

        // PIX Reversal Events
        {
          type: WebhookEventType.PIX_IN_REVERSAL_PROCESSING,
          title: "Estorno PIX Recebido - Processando",
          description: "Acionado quando um estorno de PIX recebido está sendo processado",
          detailedDescription: "Este evento é disparado quando um estorno (devolução) de um PIX recebido é solicitado e está sendo processado.",
          category: "Estornos PIX",
          payloadExample: {
            id: "cmb2rev123001yo10abc123def",
            type: "REFUND",
            status: "PROCESSING",
            amount: 75.00,
            originalTransactionId: "cmb2rephm0001yo103xz9i0gm",
            pixKey: "<EMAIL>",
            pixKeyType: "EMAIL",
            description: "Estorno PIX - Produto não entregue",
            customerName: "João Silva",
            customerEmail: "<EMAIL>",
            customerDocument: "12345678901",
            createdAt: "2024-01-15T16:45:00Z",
            organizationId: "org_123456789",
            endToEndId: null,
            externalId: "ext_rev789",
            referenceCode: "EST001",
            percentFee: 0,
            fixedFee: 0,
            totalFee: 0,
            netAmount: 75.00
          }
        },
        {
          type: WebhookEventType.PIX_IN_REVERSAL_CONFIRMATION,
          title: "Estorno PIX Recebido - Confirmado",
          description: "Acionado quando um estorno de PIX recebido é confirmado",
          detailedDescription: "Este evento confirma que o estorno foi processado e o valor foi debitado da conta, sendo devolvido ao pagador original.",
          category: "Estornos PIX",
          payloadExample: {
            id: "cmb2rev123001yo10abc123def",
            type: "REFUND",
            status: "APPROVED",
            amount: 75.00,
            originalTransactionId: "cmb2rephm0001yo103xz9i0gm",
            pixKey: "<EMAIL>",
            pixKeyType: "EMAIL",
            description: "Estorno PIX - Produto não entregue",
            customerName: "João Silva",
            customerEmail: "<EMAIL>",
            customerDocument: "12345678901",
            createdAt: "2024-01-15T16:45:00Z",
            paymentAt: "2024-01-15T16:45:30Z",
            organizationId: "org_123456789",
            endToEndId: "E12345678202401151645MNOPQR345678",
            externalId: "ext_rev789",
            referenceCode: "EST001",
            percentFee: 0,
            fixedFee: 0,
            totalFee: 0,
            netAmount: 75.00,
            previousStatus: "PROCESSING"
          }
        },
        {
          type: WebhookEventType.PIX_OUT_REVERSAL,
          title: "Estorno PIX Enviado",
          description: "Acionado quando um PIX enviado é estornado",
          detailedDescription: "Este evento é disparado quando uma transferência PIX enviada é estornada pelo destinatário ou por solicitação externa.",
          category: "Estornos PIX",
          payloadExample: {
            id: "cmb2revout001yo10xyz789abc",
            type: "REVERSAL",
            status: "APPROVED",
            amount: 50.00,
            originalTransactionId: "cmb2req920005yo10eq45ot1q",
            pixKey: "11999887766",
            pixKeyType: "PHONE",
            description: "Estorno PIX enviado",
            customerName: "Maria Santos",
            customerEmail: "<EMAIL>",
            customerDocument: "98765432100",
            createdAt: "2024-01-15T18:30:00Z",
            paymentAt: "2024-01-15T18:30:15Z",
            organizationId: "org_123456789",
            endToEndId: "E12345678202401151830STUVWX901234",
            externalId: "ext_revout456",
            referenceCode: "ESTOUT001",
            percentFee: 0,
            fixedFee: 0,
            totalFee: 0,
            netAmount: 50.00
          }
        },




      ];

      return c.json({
        data: eventTypeDescriptions,
      });
    } catch (error) {
      logger.error("Error getting webhook event types", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to get webhook event types" });
    }
  })
);
